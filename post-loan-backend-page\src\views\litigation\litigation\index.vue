<template>
  <div class="app-container">
    <!-- 启动法诉弹窗 -->
    <litigation-form ref="litigationForm" :data="currentRow" />

    <!-- 法诉费用弹窗 -->
    <litigation-fee-form ref="litigationFeeForm" :data="currentRow" />

    <!-- 提交日志弹窗 -->
    <litigation-log-form ref="litigationLogForm" :data="currentRow" />

    <!-- 日志查看弹窗 -->
    <litigation-log-view ref="litigationLogView" :data="currentRow" />

    <!-- 派单找车组件 -->
    <dispatch-vehicle-form ref="dispatchVehicleForm" :loanId="dispatchLoanId" />

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="customerName">
        <el-input v-model="queryParams.customerName" placeholder="贷款人账户、姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="certId">
        <el-input v-model="queryParams.certId" placeholder="贷款人身份证号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="车牌号码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="carStatus">
        <el-select v-model="queryParams.carStatus" placeholder="车辆状态" clearable>
          <el-option v-for="dict in carStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="jgName">
        <el-input v-model="queryParams.jgName" placeholder="录单渠道名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="litigationClerk">
        <el-input v-model="queryParams.litigationClerk" placeholder="法诉文员" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <template v-if="showMore">
        <el-form-item label="" prop="caseOwner">
          <el-input v-model="queryParams.caseOwner" placeholder="案件负责人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="" prop="litigationStatus">
          <litigation-status v-model="queryParams.litigationStatus" placeholder="法诉状态" />
        </el-form-item>
        <el-form-item label="" prop="logType">
          <el-select v-model="queryParams.logType" placeholder="日志类型" clearable>
            <el-option v-for="dict in logTypeList" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="lawsuitCourt">
          <el-select v-model="queryParams.lawsuitCourt" placeholder="诉讼法院" clearable>
            <el-option v-for="dict in lawsuitCourtList" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>
      <el-form-item style="float: right">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="text" size="mini" @click="showMore = !showMore">
          {{ showMore ? '收起' : '更多' }}
          <i :class="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="litigation_caseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="55" fixed="left" />
      <el-table-column label="法诉文员" align="center" prop="法诉文员" width="100" />
      <el-table-column label="发起法诉日" align="center" prop="发起法诉日" width="110" />
      <el-table-column label="案件启动日" align="center" prop="案件启动日" width="110" />
      <el-table-column label="日志类型" align="center" width="100">
        <template slot-scope="scope">
          {{ getUrgeStatusText(scope.row.日志类型) }}
        </template>
      </el-table-column>
      <el-table-column label="日志更新日" align="center" prop="日志更新日" width="110" />
      <el-table-column label="法诉状态" align="center" prop="法诉子状态" width="100" />
      <el-table-column label="贷款人" align="center" prop="贷款人" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="openUserInfo({ customerId: scope.row.客户ID, applyId: scope.row.申请编号 })">
            {{ scope.row.贷款人 }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="身份证" align="center" prop="身份证" width="150" />
      <el-table-column label="车辆牌号" align="center" prop="车辆牌号" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="checkCar(scope.row.车辆牌号)">{{ scope.row.车辆牌号 }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="车辆状态" align="center" width="120">
        <template slot-scope="scope">
          {{ getCarStatusText(scope.row.车辆状态) }}
        </template>
      </el-table-column>
      <el-table-column label="找车团队" align="center" width="100">
        <template #default="{ row }">
          {{ row.找车团队 || '未派单' }}
        </template>
      </el-table-column>
      <el-table-column label="地区" align="center" prop="地区" width="100" />
      <el-table-column label="出单渠道" align="center" prop="出单渠道" width="120" />
      <el-table-column label="放款银行" align="center" prop="放款银行" width="120" />
      <el-table-column label="托管类型" align="center" prop="托管类型" width="100" />
      <el-table-column label="欠款余额" align="center" width="120">
        <template slot-scope="scope">
          <div>
            <div>{{ formatMoney(scope.row.剩余金额) }}</div>
            <el-button
              type="text"
              size="mini"
              @click="showDebtDetail(scope.row)"
              style="color: #409EFF; font-size: 12px;"
            >
              详情
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="未起诉金额" align="center" width="120">
        <template slot-scope="scope">
          <div>
            <div>{{ formatMoney(calculateUnsuedAmount(scope.row)) }}</div>
            <el-button
              type="text"
              size="mini"
              @click="showUnsuedContentDetail(scope.row)"
              style="color: #409EFF; font-size: 12px;"
            >
              详情
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="起诉金额" align="center" width="120">
        <template slot-scope="scope">
          {{ formatMoney(scope.row.起诉金额) }}
        </template>
      </el-table-column>
      <el-table-column label="起诉类型" align="center" prop="起诉类型" width="100">
        <template slot-scope="scope">
          {{ getLawsuitTypeText(scope.row.起诉类型) }}
        </template>
      </el-table-column>
      <el-table-column label="起诉内容" align="center" prop="起诉内容" width="150">
        <template slot-scope="scope">
          {{ getLawsuitContentText(scope.row.起诉内容) }}
        </template>
      </el-table-column>
      <el-table-column label="判决金额" align="center" width="120">
        <template slot-scope="scope">
          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'judgmentAmount')) }}
        </template>
      </el-table-column>
      <el-table-column label="利息" align="center" width="100">
        <template slot-scope="scope">
          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'interest')) }}
        </template>
      </el-table-column>
      <el-table-column label="诉讼费" align="center" width="120">
        <template slot-scope="scope">
          <div>
            <div>{{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'litigation')) }}</div>
            <el-button
              type="text"
              size="mini"
              @click="viewLitigationFeeDetails(scope.row.序号, 'litigation')"
              style="font-size: 12px; padding: 0;">
              详情
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="待追偿欠款" align="center" width="120">
        <template slot-scope="scope">
          <div>
            <div>{{ formatMoney(calculateTotalDebt(scope.row)) }}</div>
            <el-button
              type="text"
              size="mini"
              @click="viewTotalDebtDetails(scope.row)"
              style="font-size: 12px; padding: 0;">
              详情
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="代偿证明发出日" align="center" prop="代偿证明发出日" width="140" />
      <el-table-column label="法院地" align="center" prop="法院地" width="100" />
      <el-table-column label="诉讼法院" align="center" prop="诉讼法院" width="120" />
      <el-table-column label="案件负责人" align="center" prop="案件负责人" width="100" />
      <el-table-column label="诉前调号出具时间" align="center" prop="诉前调号出具时间" width="150" />
      <el-table-column label="诉前调号" align="center" prop="诉前调号" width="120" />
      <el-table-column label="民初号出具时间" align="center" prop="民初号出具时间" width="140" />
      <el-table-column label="民初号" align="center" prop="民初号" width="120" />
      <el-table-column label="开庭时间" align="center" prop="开庭时间" width="110" />
      <el-table-column label="申请执行时间" align="center" prop="申请执行时间" width="130" />
      <el-table-column label="执行号/执保号" align="center" prop="执行号/执保号" width="140" />
      <el-table-column label="车辆出库时间" align="center" prop="车辆出库时间" width="130" />
      <el-table-column label="法拍时间" align="center" prop="法拍时间" width="110" />
      <el-table-column label="车辆评估价" align="center" width="120">
        <template slot-scope="scope">
          {{ formatMoney(scope.row.车辆评估价) }}
        </template>
      </el-table-column>
      <el-table-column label="拍卖金额" align="center" width="120">
        <template slot-scope="scope">
          {{ formatMoney(scope.row.拍卖金额) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="click" popper-class="custom-popover">
            <div class="operation-buttons">
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openLitigationLogView(scope.row)"
                v-hasPermi="['vm_car_order:vm_car_order:edit']">
                查看日志
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                v-hasPermi="['vm_car_order:vm_car_order:remove']"
                @click="openLitigationForm(scope.row)">
                启动法诉
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openLitigationLogForm(scope.row)"
                v-hasPermi="['vm_car_order:vm_car_order:edit']">
                提交日志
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openLitigationFeeForm(scope.row)"
                v-hasPermi="['vm_car_order:vm_car_order:edit']">
                法诉费用
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openDispatchVehicleForm(scope.row)"
                v-hasPermi="['vm_car_order:vm_car_order:edit']">
                派单找车
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openDailyExpenseDialog(scope.row)"
                v-hasPermi="['vm_car_order:vm_car_order:edit']">
                日常费用
              </el-button>
            </div>
            <el-button slot="reference" size="mini" type="text">更多</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 贷款人信息对话框 -->
    <userInfo ref="userInfo" :visible.sync="userInfoVisible" title="贷款人信息" :customerInfo="customerInfo" />
    <!-- 车辆信息组件 -->
    <car-info ref="carInfo" :visible.sync="carShow" title="车辆信息" :plateNo="plateNo" permission="2" />

    <!-- 欠款详情弹窗 -->
    <el-dialog title="欠款详情" :visible.sync="debtDetailVisible" width="800px" append-to-body>
      <div v-if="currentDebtRow" style="padding: 10px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4 style="margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;">银行代偿</h4>
            <div class="debt-item">
              <span class="debt-label">银行代偿金额:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.银行代偿金额) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">银行催回金额:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.银行催回金额) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">银行剩余未还:</span>
              <span class="debt-value debt-remaining">{{ formatMoney(currentDebtRow.银行剩余未还代偿金) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <h4 style="margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;">代扣金额</h4>
            <div class="debt-item">
              <span class="debt-label">代扣金额:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.代扣金额) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">代扣催回金额:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.代扣催回金额) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">代扣剩余未还:</span>
              <span class="debt-value debt-remaining">{{ formatMoney(currentDebtRow.代扣剩余未还代偿金) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <h4 style="margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;">违约金</h4>
            <div class="debt-item">
              <span class="debt-label">违约金:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.违约金) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">催回违约金:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.催回违约金金额) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">剩余未还违约金:</span>
              <span class="debt-value debt-remaining">{{ formatMoney(currentDebtRow.剩余未还违约金金额) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <h4 style="margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;">其他欠款</h4>
            <div class="debt-item">
              <span class="debt-label">其他欠款:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.其他欠款) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">催回其他欠款:</span>
              <span class="debt-value">{{ formatMoney(currentDebtRow.催回其他欠款) }}</span>
            </div>
            <div class="debt-item">
              <span class="debt-label">剩余未还其他欠款:</span>
              <span class="debt-value debt-remaining">{{ formatMoney(currentDebtRow.剩余未还其他欠款) }}</span>
            </div>
          </el-col>
        </el-row>
        <div style="margin-top: 20px; padding-top: 15px; border-top: 2px solid #409EFF; background-color: #f5f7fa; padding: 15px; border-radius: 4px;">
          <h4 style="margin-bottom: 15px; color: #409EFF;">汇总信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="debt-item total-debt">
                <span class="debt-label">总欠款金额:</span>
                <span class="debt-value">{{ formatMoney(currentDebtRow.总欠款金额) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="debt-item total-debt">
                <span class="debt-label">已还金额:</span>
                <span class="debt-value">{{ formatMoney(currentDebtRow.已还金额) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="debt-item total-debt">
                <span class="debt-label">剩余金额:</span>
                <span class="debt-value debt-remaining">{{ formatMoney(currentDebtRow.剩余金额) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLitigation, getLitigationCostSummary, getLitigationByLoanId } from '@/api/litigation/litigation'
import litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'
import litigationForm from './modules/litigationForm'
import litigationFeeForm from './modules/litigationFeeForm'
import litigationLogForm from './modules/litigationLogForm'
import litigationLogView from './modules/litigationLogView'
import userInfo from '@/layout/components/Dialog/userInfo.vue'
import carInfo from '@/layout/components/Dialog/carInfo.vue'
import dispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'

export default {
  name: 'Litigation',
  components: {
    litigationStatus,
    litigationForm,
    litigationFeeForm,
    litigationLogForm,
    litigationLogView,
    userInfo,
    carInfo,
    dispatchVehicleForm,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 法诉案件表格数据
      litigation_caseList: [],
      // 法诉费用汇总数据 - 按案件ID存储
      litigationCostSummary: {},
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '',
        certId: '',
        plateNo: '',
        carStatus: '',
        jgName: '',
        caseOwner: '',
        litigationStatus: '',
        logType: '',
        lawsuitCourt: '',
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 当前行数据
      currentRow: {},
      showMore: false,
      // 贷款人信息
      userInfoVisible: false,
      customerInfo: { customerId: '', applyId: '' },
      // 车辆信息
      carShow: false,
      plateNo: '',
      // 欠款详情
      debtDetailVisible: false,
      currentDebtRow: null,
      carStatusList: [
        { label: '省内正常行驶', value: '1' },
        { label: '省外正常行驶', value: '2' },
        { label: '抵押', value: '3' },
        { label: '疑似抵押', value: '4' },
        { label: '疑似黑车', value: '5' },
        { label: '已入库', value: '6' },
        { label: '车在法院', value: '7' },
        { label: '已法拍', value: '8' },
        { label: '协商卖车', value: '9' },
      ],
      litigationStatusList: [
        { label: '待立案', value: '1' },
        { label: '已立案', value: '2' },
        { label: '开庭', value: '3' },
        { label: '判决', value: '4' },
        { label: '结案', value: '5' },
      ],
      logTypeList: [
        { label: '电话', value: '1' },
        { label: '短信', value: '2' },
        { label: '上门', value: '3' },
        { label: '邮件', value: '4' },
        { label: '其他', value: '5' },
      ],
      lawsuitCourtList: [
        { label: '法院A', value: 'A' },
        { label: '法院B', value: 'B' },
        { label: '法院C', value: 'C' },
      ],
      lawsuitTypeList: [
        { label: '债转', value: '1' },
        { label: '债加', value: '2' },
        { label: '担保物权', value: '3' },
        { label: '仲裁', value: '4' },
        { label: '赋强公证', value: '5' },
        { label: '拍状元', value: '6' },
        { label: '拍司令', value: '7' },
        { label: '属地诉讼', value: '8' },
        { label: '余值起诉', value: '9' },
        { label: '债权出售', value: '10' },
        { label: '签约地诉讼', value: '11' },
        { label: '特殊诉讼通道', value: '12' },
      ],
      lawsuitContentList: [
        { label: '银行代偿金额', value: '1' },
        { label: '代扣金额', value: '2' },
        { label: '违约金', value: '3' },
        { label: '其他欠款', value: '4' },
      ],
      dispatchLoanId: '',
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询法诉案件列表 */
    getList() {
      this.loading = true
      listLitigation(this.queryParams).then(response => {
        this.litigation_caseList = response.rows
        this.total = response.total
        this.loading = false
        // 获取法诉费用汇总数据
        this.loadLitigationCostSummary()
      })
    },

    /** 加载法诉费用汇总数据 */
    loadLitigationCostSummary() {
      // 获取所有案件ID，确保转换为数字类型
      const caseIds = this.litigation_caseList
        .map(item => {
          const id = item.序号
          // 确保ID是数字类型
          return typeof id === 'string' ? parseInt(id) : Number(id)
        })
        .filter(id => id && !isNaN(id))

      if (caseIds.length === 0) return

      console.log('发送的案件ID列表:', caseIds)

      // 调用API获取费用汇总
      getLitigationCostSummary(caseIds).then(response => {
        if (response.code === 200) {
          this.litigationCostSummary = response.data || {}
          console.log('获取到的费用汇总数据:', this.litigationCostSummary)
        } else {
          console.error('获取法诉费用汇总失败:', response.msg)
          this.litigationCostSummary = {}
        }
      }).catch(error => {
        console.error('获取法诉费用汇总失败:', error)
        this.litigationCostSummary = {}
      })
    },

    /** 获取法诉费用金额 */
    getLitigationFeeAmount(caseId, feeType) {
      // 确保caseId是正确的类型
      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)
      const summary = this.litigationCostSummary[normalizedCaseId]

      if (!summary) {
        console.log(`未找到案件ID ${normalizedCaseId} 的费用汇总数据`)
        return 0
      }

      switch (feeType) {
        case 'judgmentAmount':
          return Number(summary.judgmentAmount || 0)
        case 'interest':
          return Number(summary.interest || 0)
        case 'litigation':
          // 诉讼费包含多种费用类型的总和
          return Number(summary.lawyerFee || 0) +
                 Number(summary.litigationFee || 0) +
                 Number(summary.preservationFee || 0) +
                 Number(summary.surveillanceFee || 0) +
                 Number(summary.announcementFee || 0) +
                 Number(summary.appraisalFee || 0) +
                 Number(summary.executionFee || 0) +
                 Number(summary.penalty || 0) +
                 Number(summary.guaranteeFee || 0) +
                 Number(summary.intermediaryFee || 0) +
                 Number(summary.compensity || 0) +
                 Number(summary.otherAmountsOwed || 0) +
                 Number(summary.insurance || 0)
        default:
          return 0
      }
    },

    /** 查看诉讼费详情 */
    viewLitigationFeeDetails(caseId, feeType) {
      // 确保caseId是正确的类型
      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)

      // 只处理诉讼费详情
      if (feeType === 'litigation') {
        const title = '诉讼费详情'
        const content = this.formatLitigationFeeDetail(normalizedCaseId, [
          'lawyerFee', 'litigationFee', 'preservationFee', 'surveillanceFee',
          'announcementFee', 'appraisalFee', 'executionFee', 'penalty',
          'guaranteeFee', 'intermediaryFee', 'compensity', 'otherAmountsOwed', 'insurance'
        ])

        this.$alert(content, title, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        })
      }
    },

    /** 格式化法诉费用详情 */
    formatLitigationFeeDetail(caseId, feeTypes) {
      const summary = this.litigationCostSummary[caseId]
      if (!summary) return '<p>暂无费用数据</p>'

      const feeLabels = {
        judgmentAmount: '判决金额',
        interest: '利息',
        lawyerFee: '律师费',
        litigationFee: '法诉费',
        preservationFee: '保全费',
        surveillanceFee: '布控费',
        announcementFee: '公告费',
        appraisalFee: '评估费',
        executionFee: '执行费',
        penalty: '违约金',
        guaranteeFee: '担保费',
        intermediaryFee: '居间费',
        compensity: '代偿金',
        otherAmountsOwed: '其他欠款',
        insurance: '保险费'
      }

      let html = '<div style="text-align: left;">'
      let total = 0

      feeTypes.forEach(feeType => {
        const amount = Number(summary[feeType] || 0)
        if (amount > 0) {
          html += `<p>${feeLabels[feeType]}: ￥${this.formatMoney(amount)}</p>`
          total += amount
        }
      })

      if (feeTypes.length > 1 && total > 0) {
        html += `<hr><p><strong>合计: ￥${this.formatMoney(total)}</strong></p>`
      }

      html += '</div>'
      return html || '<p>暂无费用数据</p>'
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.queryParams = {}
      this.getList()
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 启动法诉弹窗
    openLitigationForm(row) {
      this.currentRow = row || {}
      this.$refs.litigationForm.open && this.$refs.litigationForm.open()
    },
    onLitigationFormConfirm() {
      // 处理确认逻辑
    },
    // 法诉费用弹窗
    openLitigationFeeForm(row) {
      this.currentRow = row || {}
      this.$refs.litigationFeeForm.open && this.$refs.litigationFeeForm.open()
    },
    onLitigationFeeFormConfirm() {
      // 处理确认逻辑
    },
    // 提交日志弹窗
    openLitigationLogForm(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.$refs.litigationLogForm.openDialog && this.$refs.litigationLogForm.openDialog()
    },
    onLitigationLogFormConfirm() {
      // 处理确认逻辑
    },
    // 日志查看弹窗
    openLitigationLogView(row) {
      this.currentRow = row || {}
      this.$refs.litigationLogView.openDialog && this.$refs.litigationLogView.openDialog()
    },
    // 找车按钮弹窗
    openDispatchVehicleForm(row) {
      this.dispatchLoanId = row.流程序号
      this.$refs.dispatchVehicleForm.openDialog()
    },
    /** 计算待追偿欠款总额 */
    calculateTotalDebt(row) {
      const caseId = row.序号

      // 获取各项费用
      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount') // 判决金额
      const interest = this.getLitigationFeeAmount(caseId, 'interest') // 利息
      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation') // 法诉费用
      const unsuedAmount = this.calculateUnsuedAmount(row) // 未起诉金额

      // 计算总额：判决金额 + 利息 + 法诉费用 + 未起诉金额
      const total = Number(judgmentAmount) + Number(interest) + Number(litigationCosts) + Number(unsuedAmount)

      return total
    },

    /** 查看待追偿欠款详情 */
    viewTotalDebtDetails(row) {
      const caseId = row.序号

      // 获取各项费用
      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount')
      const interest = this.getLitigationFeeAmount(caseId, 'interest')
      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation')
      const unsuedAmount = this.calculateUnsuedAmount(row)
      const total = this.calculateTotalDebt(row)

      let html = '<div style="text-align: left;">'
      html += `<p>判决金额: ￥${this.formatMoney(judgmentAmount)}</p>`
      html += `<p>利息: ￥${this.formatMoney(interest)}</p>`
      html += `<p>法诉费用: ￥${this.formatMoney(litigationCosts)}</p>`
      html += `<p>未起诉金额: ￥${this.formatMoney(unsuedAmount)}</p>`
      html += `<hr><p><strong>待追偿欠款总计: ￥${this.formatMoney(total)}</strong></p>`
      html += '</div>'

      this.$alert(html, '待追偿欠款详情', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      })
    },

    // 打开日常费用申请弹窗
    openDailyExpenseDialog(row) {
      // 通过ref调用法诉费用表单组件的方法，传入案件ID
      this.$refs.litigationFeeForm.openDailyExpenseDialog(row.序号)
    },
    // 查看贷款人信息
    openUserInfo(customerInfo) {
      this.customerInfo = customerInfo
      this.userInfoVisible = true
    },
    // 查看车辆信息
    checkCar(plateNo) {
      this.plateNo = plateNo
      this.carShow = true
    },
    // 获取起诉类型文字
    getLawsuitTypeText(value) {
      const item = this.lawsuitTypeList.find(item => item.value === value)
      return item ? item.label : value
    },
    // 获取起诉内容文字
    getLawsuitContentText(value) {
      if (!value) return '-'

      try {
        // 尝试解析JSON数组（多选格式）
        const contentArray = JSON.parse(value)
        if (Array.isArray(contentArray)) {
          return contentArray.map(val => {
            const item = this.lawsuitContentList.find(item => item.value === val)
            return item ? item.label : val
          }).join('、')
        }
      } catch {
        // 如果不是JSON格式，按单选处理
        const item = this.lawsuitContentList.find(item => item.value === value)
        return item ? item.label : value
      }

      // 默认返回原值
      return value
    },
    // 获取催记类型文本
    getUrgeStatusText(value) {
      const urgeStatusMap = {
        1: '继续跟踪',
        2: '约定还款',
        3: '无法跟进'
      }
      return urgeStatusMap[value] || value
    },
    // 获取车辆状态文本
    getCarStatusText(value) {
      // 使用已有的 carStatusList 数据
      const statusItem = this.carStatusList.find(item => item.value == value)
      return statusItem ? statusItem.label : value
    },
    // 显示欠款详情弹窗
    showDebtDetail(row) {
      this.currentDebtRow = row
      this.debtDetailVisible = true
    },
    // 格式化金额显示
    formatMoney(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '0.00'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    // 计算未起诉金额
    calculateUnsuedAmount(row) {
      const remainingAmount = Number(row.剩余金额) || 0
      const suedAmount = Number(row.起诉金额) || 0
      const unsuedAmount = remainingAmount - suedAmount
      return unsuedAmount > 0 ? unsuedAmount : 0
    },
  },
}
</script>

<style scoped>
/* 操作按钮容器 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 90px;
  padding: 4px 8px;
}

/* 操作按钮样式 */
.operation-btn {
  width: 74px !important;
  height: 26px !important;
  margin: 0 !important;
  padding: 0 6px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  line-height: 26px;
}

/* 按钮悬停效果 */
.operation-btn:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

/* 欠款详情样式 */
.debt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 5px 0;
}

.debt-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.debt-value {
  font-size: 13px;
  color: #303133;
  font-weight: 600;
}

.debt-remaining {
  color: #F56C6C !important;
}

.total-debt {
  font-size: 14px;
  font-weight: bold;
}

.total-debt .debt-label {
  color: #303133;
  font-weight: 600;
}

.total-debt .debt-value {
  font-size: 15px;
  color: #409EFF;
}
</style>
<style>
.custom-popover {
  width: 116px !important;
  min-width: 116px !important;
  max-width: 116px !important;
  box-sizing: border-box !important;
}
</style>

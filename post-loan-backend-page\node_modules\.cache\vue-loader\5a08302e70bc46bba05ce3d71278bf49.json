{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\index.vue?vue&type=style&index=0&id=0e705708&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\index.vue", "mtime": 1754116244943}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog5pON5L2c5oyJ6ZKu5a655ZmoICovDQoub3BlcmF0aW9uLWJ1dHRvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCiAgd2lkdGg6IDkwcHg7DQogIHBhZGRpbmc6IDRweCA4cHg7DQp9DQoNCi8qIOaTjeS9nOaMiemSruagt+W8jyAqLw0KLm9wZXJhdGlvbi1idG4gew0KICB3aWR0aDogNzRweCAhaW1wb3J0YW50Ow0KICBoZWlnaHQ6IDI2cHggIWltcG9ydGFudDsNCiAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7DQogIHBhZGRpbmc6IDAgNnB4ICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGxpbmUtaGVpZ2h0OiAyNnB4Ow0KfQ0KDQovKiDmjInpkq7mgqzlgZzmlYjmnpwgKi8NCi5vcGVyYXRpb24tYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi8qIOasoOasvuivpuaDheagt+W8jyAqLw0KLmRlYnQtaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBwYWRkaW5nOiA1cHggMDsNCn0NCg0KLmRlYnQtbGFiZWwgew0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouZGVidC12YWx1ZSB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5kZWJ0LXJlbWFpbmluZyB7DQogIGNvbG9yOiAjRjU2QzZDICFpbXBvcnRhbnQ7DQp9DQoNCi50b3RhbC1kZWJ0IHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLnRvdGFsLWRlYnQgLmRlYnQtbGFiZWwgew0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnRvdGFsLWRlYnQgLmRlYnQtdmFsdWUgew0KICBmb250LXNpemU6IDE1cHg7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/litigation/litigation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 启动法诉弹窗 -->\r\n    <litigation-form ref=\"litigationForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 法诉费用弹窗 -->\r\n    <litigation-fee-form ref=\"litigationFeeForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 提交日志弹窗 -->\r\n    <litigation-log-form ref=\"litigationLogForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 日志查看弹窗 -->\r\n    <litigation-log-view ref=\"litigationLogView\" :data=\"currentRow\" />\r\n\r\n    <!-- 派单找车组件 -->\r\n    <dispatch-vehicle-form ref=\"dispatchVehicleForm\" :loanId=\"dispatchLoanId\" />\r\n\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input v-model=\"queryParams.certId\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"车牌号码\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"carStatus\">\r\n        <el-select v-model=\"queryParams.carStatus\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"litigationClerk\">\r\n        <el-input v-model=\"queryParams.litigationClerk\" placeholder=\"法诉文员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <template v-if=\"showMore\">\r\n        <el-form-item label=\"\" prop=\"caseOwner\">\r\n          <el-input v-model=\"queryParams.caseOwner\" placeholder=\"案件负责人\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"litigationStatus\">\r\n          <litigation-status v-model=\"queryParams.litigationStatus\" placeholder=\"法诉状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"logType\">\r\n          <el-select v-model=\"queryParams.logType\" placeholder=\"日志类型\" clearable>\r\n            <el-option v-for=\"dict in logTypeList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"lawsuitCourt\">\r\n          <el-select v-model=\"queryParams.lawsuitCourt\" placeholder=\"诉讼法院\" clearable>\r\n            <el-option v-for=\"dict in lawsuitCourtList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n      <el-form-item style=\"float: right\">\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"text\" size=\"mini\" @click=\"showMore = !showMore\">\r\n          {{ showMore ? '收起' : '更多' }}\r\n          <i :class=\"showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"litigation_caseList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"法诉文员\" align=\"center\" prop=\"法诉文员\" width=\"100\" />\r\n      <el-table-column label=\"发起法诉日\" align=\"center\" prop=\"发起法诉日\" width=\"110\" />\r\n      <el-table-column label=\"案件启动日\" align=\"center\" prop=\"案件启动日\" width=\"110\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getUrgeStatusText(scope.row.日志类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"日志更新日\" align=\"center\" prop=\"日志更新日\" width=\"110\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"法诉子状态\" width=\"100\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"贷款人\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openUserInfo({ customerId: scope.row.客户ID, applyId: scope.row.申请编号 })\">\r\n            {{ scope.row.贷款人 }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"身份证\" align=\"center\" prop=\"身份证\" width=\"150\" />\r\n      <el-table-column label=\"车辆牌号\" align=\"center\" prop=\"车辆牌号\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"checkCar(scope.row.车辆牌号)\">{{ scope.row.车辆牌号 }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆状态\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getCarStatusText(scope.row.车辆状态) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"找车团队\" align=\"center\" width=\"100\">\r\n        <template #default=\"{ row }\">\r\n          {{ row.找车团队 || '未派单' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"地区\" width=\"100\" />\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"出单渠道\" width=\"120\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"放款银行\" width=\"120\" />\r\n      <el-table-column label=\"托管类型\" align=\"center\" prop=\"托管类型\" width=\"100\" />\r\n      <el-table-column label=\"欠款余额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(scope.row.剩余金额) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showDebtDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateUnsuedAmount(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showUnsuedContentDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.起诉金额) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉类型\" align=\"center\" prop=\"起诉类型\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitTypeText(scope.row.起诉类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉内容\" align=\"center\" prop=\"起诉内容\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitContentText(scope.row.起诉内容) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"判决金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'judgmentAmount')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"利息\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'interest')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"诉讼费\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'litigation')) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewLitigationFeeDetails(scope.row.序号, 'litigation')\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"待追偿欠款\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateTotalDebt(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewTotalDebtDetails(scope.row)\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"代偿证明发出日\" align=\"center\" prop=\"代偿证明发出日\" width=\"140\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"法院地\" width=\"100\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"诉讼法院\" width=\"120\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"案件负责人\" width=\"100\" />\r\n      <el-table-column label=\"诉前调号出具时间\" align=\"center\" prop=\"诉前调号出具时间\" width=\"150\" />\r\n      <el-table-column label=\"诉前调号\" align=\"center\" prop=\"诉前调号\" width=\"120\" />\r\n      <el-table-column label=\"民初号出具时间\" align=\"center\" prop=\"民初号出具时间\" width=\"140\" />\r\n      <el-table-column label=\"民初号\" align=\"center\" prop=\"民初号\" width=\"120\" />\r\n      <el-table-column label=\"开庭时间\" align=\"center\" prop=\"开庭时间\" width=\"110\" />\r\n      <el-table-column label=\"申请执行时间\" align=\"center\" prop=\"申请执行时间\" width=\"130\" />\r\n      <el-table-column label=\"执行号/执保号\" align=\"center\" prop=\"执行号/执保号\" width=\"140\" />\r\n      <el-table-column label=\"车辆出库时间\" align=\"center\" prop=\"车辆出库时间\" width=\"130\" />\r\n      <el-table-column label=\"法拍时间\" align=\"center\" prop=\"法拍时间\" width=\"110\" />\r\n      <el-table-column label=\"车辆评估价\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.车辆评估价) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"拍卖金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.拍卖金额) }}\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogView(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                查看日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n                @click=\"openLitigationForm(scope.row)\">\r\n                启动法诉\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                提交日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationFeeForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                法诉费用\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDispatchVehicleForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                派单找车\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDailyExpenseDialog(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                日常费用\r\n              </el-button>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <car-info ref=\"carInfo\" :visible.sync=\"carShow\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n\r\n    <!-- 欠款详情弹窗 -->\r\n    <el-dialog title=\"欠款详情\" :visible.sync=\"debtDetailVisible\" width=\"800px\" append-to-body>\r\n      <div v-if=\"currentDebtRow\" style=\"padding: 10px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">银行代偿</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行代偿金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行代偿金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.银行剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">代扣金额</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.代扣剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">违约金</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.违约金) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回违约金金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还违约金:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还违约金金额) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">其他欠款</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还其他欠款:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还其他欠款) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <div style=\"margin-top: 20px; padding-top: 15px; border-top: 2px solid #409EFF; background-color: #f5f7fa; padding: 15px; border-radius: 4px;\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">汇总信息</h4>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">总欠款金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.总欠款金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">已还金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.已还金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">剩余金额:</span>\r\n                <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation, getLitigationCostSummary, getLitigationByLoanId } from '@/api/litigation/litigation'\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport litigationForm from './modules/litigationForm'\r\nimport litigationFeeForm from './modules/litigationFeeForm'\r\nimport litigationLogForm from './modules/litigationLogForm'\r\nimport litigationLogView from './modules/litigationLogView'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport dispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'\r\n\r\nexport default {\r\n  name: 'Litigation',\r\n  components: {\r\n    litigationStatus,\r\n    litigationForm,\r\n    litigationFeeForm,\r\n    litigationLogForm,\r\n    litigationLogView,\r\n    userInfo,\r\n    carInfo,\r\n    dispatchVehicleForm,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 法诉案件表格数据\r\n      litigation_caseList: [],\r\n      // 法诉费用汇总数据 - 按案件ID存储\r\n      litigationCostSummary: {},\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: '',\r\n        certId: '',\r\n        plateNo: '',\r\n        carStatus: '',\r\n        jgName: '',\r\n        caseOwner: '',\r\n        litigationStatus: '',\r\n        logType: '',\r\n        lawsuitCourt: '',\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前行数据\r\n      currentRow: {},\r\n      showMore: false,\r\n      // 贷款人信息\r\n      userInfoVisible: false,\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      // 车辆信息\r\n      carShow: false,\r\n      plateNo: '',\r\n      // 欠款详情\r\n      debtDetailVisible: false,\r\n      currentDebtRow: null,\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      litigationStatusList: [\r\n        { label: '待立案', value: '1' },\r\n        { label: '已立案', value: '2' },\r\n        { label: '开庭', value: '3' },\r\n        { label: '判决', value: '4' },\r\n        { label: '结案', value: '5' },\r\n      ],\r\n      logTypeList: [\r\n        { label: '电话', value: '1' },\r\n        { label: '短信', value: '2' },\r\n        { label: '上门', value: '3' },\r\n        { label: '邮件', value: '4' },\r\n        { label: '其他', value: '5' },\r\n      ],\r\n      lawsuitCourtList: [\r\n        { label: '法院A', value: 'A' },\r\n        { label: '法院B', value: 'B' },\r\n        { label: '法院C', value: 'C' },\r\n      ],\r\n      lawsuitTypeList: [\r\n        { label: '债转', value: '1' },\r\n        { label: '债加', value: '2' },\r\n        { label: '担保物权', value: '3' },\r\n        { label: '仲裁', value: '4' },\r\n        { label: '赋强公证', value: '5' },\r\n        { label: '拍状元', value: '6' },\r\n        { label: '拍司令', value: '7' },\r\n        { label: '属地诉讼', value: '8' },\r\n        { label: '余值起诉', value: '9' },\r\n        { label: '债权出售', value: '10' },\r\n        { label: '签约地诉讼', value: '11' },\r\n        { label: '特殊诉讼通道', value: '12' },\r\n      ],\r\n      lawsuitContentList: [\r\n        { label: '银行代偿金额', value: '1' },\r\n        { label: '代扣金额', value: '2' },\r\n        { label: '违约金', value: '3' },\r\n        { label: '其他欠款', value: '4' },\r\n      ],\r\n      dispatchLoanId: '',\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉案件列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigation(this.queryParams).then(response => {\r\n        this.litigation_caseList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n        // 获取法诉费用汇总数据\r\n        this.loadLitigationCostSummary()\r\n      })\r\n    },\r\n\r\n    /** 加载法诉费用汇总数据 */\r\n    loadLitigationCostSummary() {\r\n      // 获取所有案件ID，确保转换为数字类型\r\n      const caseIds = this.litigation_caseList\r\n        .map(item => {\r\n          const id = item.序号\r\n          // 确保ID是数字类型\r\n          return typeof id === 'string' ? parseInt(id) : Number(id)\r\n        })\r\n        .filter(id => id && !isNaN(id))\r\n\r\n      if (caseIds.length === 0) return\r\n\r\n      console.log('发送的案件ID列表:', caseIds)\r\n\r\n      // 调用API获取费用汇总\r\n      getLitigationCostSummary(caseIds).then(response => {\r\n        if (response.code === 200) {\r\n          this.litigationCostSummary = response.data || {}\r\n          console.log('获取到的费用汇总数据:', this.litigationCostSummary)\r\n        } else {\r\n          console.error('获取法诉费用汇总失败:', response.msg)\r\n          this.litigationCostSummary = {}\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取法诉费用汇总失败:', error)\r\n        this.litigationCostSummary = {}\r\n      })\r\n    },\r\n\r\n    /** 获取法诉费用金额 */\r\n    getLitigationFeeAmount(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n      const summary = this.litigationCostSummary[normalizedCaseId]\r\n\r\n      if (!summary) {\r\n        console.log(`未找到案件ID ${normalizedCaseId} 的费用汇总数据`)\r\n        return 0\r\n      }\r\n\r\n      switch (feeType) {\r\n        case 'judgmentAmount':\r\n          return Number(summary.judgmentAmount || 0)\r\n        case 'interest':\r\n          return Number(summary.interest || 0)\r\n        case 'litigation':\r\n          // 诉讼费包含多种费用类型的总和\r\n          return Number(summary.lawyerFee || 0) +\r\n                 Number(summary.litigationFee || 0) +\r\n                 Number(summary.preservationFee || 0) +\r\n                 Number(summary.surveillanceFee || 0) +\r\n                 Number(summary.announcementFee || 0) +\r\n                 Number(summary.appraisalFee || 0) +\r\n                 Number(summary.executionFee || 0) +\r\n                 Number(summary.penalty || 0) +\r\n                 Number(summary.guaranteeFee || 0) +\r\n                 Number(summary.intermediaryFee || 0) +\r\n                 Number(summary.compensity || 0) +\r\n                 Number(summary.otherAmountsOwed || 0) +\r\n                 Number(summary.insurance || 0)\r\n        default:\r\n          return 0\r\n      }\r\n    },\r\n\r\n    /** 查看诉讼费详情 */\r\n    viewLitigationFeeDetails(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n\r\n      // 只处理诉讼费详情\r\n      if (feeType === 'litigation') {\r\n        const title = '诉讼费详情'\r\n        const content = this.formatLitigationFeeDetail(normalizedCaseId, [\r\n          'lawyerFee', 'litigationFee', 'preservationFee', 'surveillanceFee',\r\n          'announcementFee', 'appraisalFee', 'executionFee', 'penalty',\r\n          'guaranteeFee', 'intermediaryFee', 'compensity', 'otherAmountsOwed', 'insurance'\r\n        ])\r\n\r\n        this.$alert(content, title, {\r\n          dangerouslyUseHTMLString: true,\r\n          confirmButtonText: '确定'\r\n        })\r\n      }\r\n    },\r\n\r\n    /** 格式化法诉费用详情 */\r\n    formatLitigationFeeDetail(caseId, feeTypes) {\r\n      const summary = this.litigationCostSummary[caseId]\r\n      if (!summary) return '<p>暂无费用数据</p>'\r\n\r\n      const feeLabels = {\r\n        judgmentAmount: '判决金额',\r\n        interest: '利息',\r\n        lawyerFee: '律师费',\r\n        litigationFee: '法诉费',\r\n        preservationFee: '保全费',\r\n        surveillanceFee: '布控费',\r\n        announcementFee: '公告费',\r\n        appraisalFee: '评估费',\r\n        executionFee: '执行费',\r\n        penalty: '违约金',\r\n        guaranteeFee: '担保费',\r\n        intermediaryFee: '居间费',\r\n        compensity: '代偿金',\r\n        otherAmountsOwed: '其他欠款',\r\n        insurance: '保险费'\r\n      }\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      let total = 0\r\n\r\n      feeTypes.forEach(feeType => {\r\n        const amount = Number(summary[feeType] || 0)\r\n        if (amount > 0) {\r\n          html += `<p>${feeLabels[feeType]}: ￥${this.formatMoney(amount)}</p>`\r\n          total += amount\r\n        }\r\n      })\r\n\r\n      if (feeTypes.length > 1 && total > 0) {\r\n        html += `<hr><p><strong>合计: ￥${this.formatMoney(total)}</strong></p>`\r\n      }\r\n\r\n      html += '</div>'\r\n      return html || '<p>暂无费用数据</p>'\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {}\r\n      this.getList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 启动法诉弹窗\r\n    openLitigationForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationForm.open && this.$refs.litigationForm.open()\r\n    },\r\n    onLitigationFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 法诉费用弹窗\r\n    openLitigationFeeForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationFeeForm.open && this.$refs.litigationFeeForm.open()\r\n    },\r\n    onLitigationFeeFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 提交日志弹窗\r\n    openLitigationLogForm(row) {\r\n      this.currentRow = JSON.parse(JSON.stringify(row))\r\n      this.$refs.litigationLogForm.openDialog && this.$refs.litigationLogForm.openDialog()\r\n    },\r\n    onLitigationLogFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 日志查看弹窗\r\n    openLitigationLogView(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationLogView.openDialog && this.$refs.litigationLogView.openDialog()\r\n    },\r\n    // 找车按钮弹窗\r\n    openDispatchVehicleForm(row) {\r\n      this.dispatchLoanId = row.流程序号\r\n      this.$refs.dispatchVehicleForm.openDialog()\r\n    },\r\n    /** 计算待追偿欠款总额 */\r\n    calculateTotalDebt(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount') // 判决金额\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest') // 利息\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation') // 法诉费用\r\n      const unsuedAmount = this.calculateUnsuedAmount(row) // 未起诉金额\r\n\r\n      // 计算总额：判决金额 + 利息 + 法诉费用 + 未起诉金额\r\n      const total = Number(judgmentAmount) + Number(interest) + Number(litigationCosts) + Number(unsuedAmount)\r\n\r\n      return total\r\n    },\r\n\r\n    /** 查看待追偿欠款详情 */\r\n    viewTotalDebtDetails(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount')\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest')\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation')\r\n      const unsuedAmount = this.calculateUnsuedAmount(row)\r\n      const total = this.calculateTotalDebt(row)\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      html += `<p>判决金额: ￥${this.formatMoney(judgmentAmount)}</p>`\r\n      html += `<p>利息: ￥${this.formatMoney(interest)}</p>`\r\n      html += `<p>法诉费用: ￥${this.formatMoney(litigationCosts)}</p>`\r\n      html += `<p>未起诉金额: ￥${this.formatMoney(unsuedAmount)}</p>`\r\n      html += `<hr><p><strong>待追偿欠款总计: ￥${this.formatMoney(total)}</strong></p>`\r\n      html += '</div>'\r\n\r\n      this.$alert(html, '待追偿欠款详情', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '确定'\r\n      })\r\n    },\r\n\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(row) {\r\n      // 通过ref调用法诉费用表单组件的方法，传入案件ID\r\n      this.$refs.litigationFeeForm.openDailyExpenseDialog(row.序号)\r\n    },\r\n    // 查看贷款人信息\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    // 查看车辆信息\r\n    checkCar(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carShow = true\r\n    },\r\n    // 获取起诉类型文字\r\n    getLawsuitTypeText(value) {\r\n      const item = this.lawsuitTypeList.find(item => item.value === value)\r\n      return item ? item.label : value\r\n    },\r\n    // 获取起诉内容文字\r\n    getLawsuitContentText(value) {\r\n      if (!value) return '-'\r\n\r\n      try {\r\n        // 尝试解析JSON数组（多选格式）\r\n        const contentArray = JSON.parse(value)\r\n        if (Array.isArray(contentArray)) {\r\n          return contentArray.map(val => {\r\n            const item = this.lawsuitContentList.find(item => item.value === val)\r\n            return item ? item.label : val\r\n          }).join('、')\r\n        }\r\n      } catch {\r\n        // 如果不是JSON格式，按单选处理\r\n        const item = this.lawsuitContentList.find(item => item.value === value)\r\n        return item ? item.label : value\r\n      }\r\n\r\n      // 默认返回原值\r\n      return value\r\n    },\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value\r\n    },\r\n    // 获取车辆状态文本\r\n    getCarStatusText(value) {\r\n      // 使用已有的 carStatusList 数据\r\n      const statusItem = this.carStatusList.find(item => item.value == value)\r\n      return statusItem ? statusItem.label : value\r\n    },\r\n    // 显示欠款详情弹窗\r\n    showDebtDetail(row) {\r\n      this.currentDebtRow = row\r\n      this.debtDetailVisible = true\r\n    },\r\n    // 格式化金额显示\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n    // 计算未起诉金额\r\n    calculateUnsuedAmount(row) {\r\n      const remainingAmount = Number(row.剩余金额) || 0\r\n      const suedAmount = Number(row.起诉金额) || 0\r\n      const unsuedAmount = remainingAmount - suedAmount\r\n      return unsuedAmount > 0 ? unsuedAmount : 0\r\n    },\r\n\r\n    // 显示未起诉内容详情\r\n    async showUnsuedContentDetail(row) {\r\n      try {\r\n        // 获取该贷款的所有法诉案件\r\n        const response = await getLitigationByLoanId(row.流程序号)\r\n\r\n        if (response.code === 200 && response.data) {\r\n          // 获取所有已启动的法诉案件的起诉内容\r\n          const usedProsecutionContents = []\r\n\r\n          response.data.forEach(caseItem => {\r\n            // 判断法诉是否已启动\r\n            if (this.isLitigationStarted(caseItem)) {\r\n              const content = caseItem.prosecutionContent\r\n              if (content) {\r\n                try {\r\n                  // 尝试解析为数组（多选）\r\n                  const contentArray = JSON.parse(content)\r\n                  if (Array.isArray(contentArray)) {\r\n                    usedProsecutionContents.push(...contentArray)\r\n                  } else {\r\n                    usedProsecutionContents.push(content)\r\n                  }\r\n                } catch {\r\n                  // 如果不是JSON格式，按单选处理\r\n                  usedProsecutionContents.push(content)\r\n                }\r\n              }\r\n            }\r\n          })\r\n\r\n          // 去重已使用的起诉内容\r\n          const uniqueUsedContents = [...new Set(usedProsecutionContents)]\r\n\r\n          // 找出未使用的起诉内容\r\n          const allProsecutionContents = this.lawsuitContentList\r\n          const unusedContents = allProsecutionContents.filter(item =>\r\n            !uniqueUsedContents.includes(item.value)\r\n          )\r\n\r\n          // 计算未起诉内容对应的金额\r\n          let html = '<div style=\"text-align: left;\">'\r\n          let totalUnsuedAmount = 0\r\n\r\n          if (unusedContents.length > 0) {\r\n            html += '<h4 style=\"margin-bottom: 15px; color: #303133;\">未起诉内容：</h4>'\r\n\r\n            unusedContents.forEach(content => {\r\n              let amount = 0\r\n              // 根据起诉内容类型计算对应金额\r\n              switch (content.value) {\r\n                case '1': // 银行代偿金额\r\n                  amount = Number(row.银行剩余未还代偿金) || 0\r\n                  break\r\n                case '2': // 代扣金额\r\n                  amount = Number(row.代扣剩余未还代偿金) || 0\r\n                  break\r\n                case '3': // 违约金\r\n                  amount = Number(row.剩余未还违约金金额) || 0\r\n                  break\r\n                case '4': // 其他欠款\r\n                  amount = Number(row.剩余未还其他欠款) || 0\r\n                  break\r\n              }\r\n\r\n              if (amount > 0) {\r\n                html += `<p>${content.label}: ￥${this.formatMoney(amount)}</p>`\r\n                totalUnsuedAmount += amount\r\n              }\r\n            })\r\n\r\n            if (totalUnsuedAmount > 0) {\r\n              html += `<hr><p><strong>未起诉金额合计: ￥${this.formatMoney(totalUnsuedAmount)}</strong></p>`\r\n            }\r\n          } else {\r\n            html += '<p>所有起诉内容都已被选择</p>'\r\n          }\r\n\r\n          html += '</div>'\r\n\r\n          this.$alert(html, '未起诉内容详情', {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取未起诉内容详情失败:', error)\r\n        this.$message.error('获取未起诉内容详情失败')\r\n      }\r\n    },\r\n\r\n    // 判断法诉是否已启动\r\n    isLitigationStarted(caseData) {\r\n      // 检查关键字段是否都有值\r\n      const hasLitigationType = caseData.litigationType != null && caseData.litigationType !== ''\r\n      const hasProsecutionType = caseData.prosecutionType != null && caseData.prosecutionType !== ''\r\n      const hasProsecutionContent = caseData.prosecutionContent != null && caseData.prosecutionContent !== ''\r\n      const hasLitigationStartDay = caseData.litigationStartDay != null && caseData.litigationStartDay !== ''\r\n\r\n      return hasLitigationType && hasProsecutionType && hasProsecutionContent && hasLitigationStartDay\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 6px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n\r\n/* 欠款详情样式 */\r\n.debt-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.debt-label {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.debt-value {\r\n  font-size: 13px;\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.debt-remaining {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.total-debt {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.total-debt .debt-label {\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.total-debt .debt-value {\r\n  font-size: 15px;\r\n  color: #409EFF;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\index.vue", "mtime": 1754115745758}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TGl0aWdhdGlvbiwgZ2V0TGl0aWdhdGlvbkNvc3RTdW1tYXJ5LCBnZXRMaXRpZ2F0aW9uQnlMb2FuSWQgfSBmcm9tICdAL2FwaS9saXRpZ2F0aW9uL2xpdGlnYXRpb24nDQppbXBvcnQgbGl0aWdhdGlvblN0YXR1cyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9saXRpZ2F0aW9uU3RhdHVzLnZ1ZScNCmltcG9ydCBsaXRpZ2F0aW9uRm9ybSBmcm9tICcuL21vZHVsZXMvbGl0aWdhdGlvbkZvcm0nDQppbXBvcnQgbGl0aWdhdGlvbkZlZUZvcm0gZnJvbSAnLi9tb2R1bGVzL2xpdGlnYXRpb25GZWVGb3JtJw0KaW1wb3J0IGxpdGlnYXRpb25Mb2dGb3JtIGZyb20gJy4vbW9kdWxlcy9saXRpZ2F0aW9uTG9nRm9ybScNCmltcG9ydCBsaXRpZ2F0aW9uTG9nVmlldyBmcm9tICcuL21vZHVsZXMvbGl0aWdhdGlvbkxvZ1ZpZXcnDQppbXBvcnQgdXNlckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvdXNlckluZm8udnVlJw0KaW1wb3J0IGNhckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvY2FySW5mby52dWUnDQppbXBvcnQgZGlzcGF0Y2hWZWhpY2xlRm9ybSBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9kaXNwYXRjaFZlaGljbGVGb3JtLnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGl0aWdhdGlvbicsDQogIGNvbXBvbmVudHM6IHsNCiAgICBsaXRpZ2F0aW9uU3RhdHVzLA0KICAgIGxpdGlnYXRpb25Gb3JtLA0KICAgIGxpdGlnYXRpb25GZWVGb3JtLA0KICAgIGxpdGlnYXRpb25Mb2dGb3JtLA0KICAgIGxpdGlnYXRpb25Mb2dWaWV3LA0KICAgIHVzZXJJbmZvLA0KICAgIGNhckluZm8sDQogICAgZGlzcGF0Y2hWZWhpY2xlRm9ybSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOazleivieahiOS7tuihqOagvOaVsOaNrg0KICAgICAgbGl0aWdhdGlvbl9jYXNlTGlzdDogW10sDQogICAgICAvLyDms5Xor4notLnnlKjmsYfmgLvmlbDmja4gLSDmjInmoYjku7ZJROWtmOWCqA0KICAgICAgbGl0aWdhdGlvbkNvc3RTdW1tYXJ5OiB7fSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBjdXN0b21lck5hbWU6ICcnLA0KICAgICAgICBjZXJ0SWQ6ICcnLA0KICAgICAgICBwbGF0ZU5vOiAnJywNCiAgICAgICAgY2FyU3RhdHVzOiAnJywNCiAgICAgICAgamdOYW1lOiAnJywNCiAgICAgICAgY2FzZU93bmVyOiAnJywNCiAgICAgICAgbGl0aWdhdGlvblN0YXR1czogJycsDQogICAgICAgIGxvZ1R5cGU6ICcnLA0KICAgICAgICBsYXdzdWl0Q291cnQ6ICcnLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fSwNCiAgICAgIC8vIOW9k+WJjeihjOaVsOaNrg0KICAgICAgY3VycmVudFJvdzoge30sDQogICAgICBzaG93TW9yZTogZmFsc2UsDQogICAgICAvLyDotLfmrL7kurrkv6Hmga8NCiAgICAgIHVzZXJJbmZvVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXN0b21lckluZm86IHsgY3VzdG9tZXJJZDogJycsIGFwcGx5SWQ6ICcnIH0sDQogICAgICAvLyDovabovobkv6Hmga8NCiAgICAgIGNhclNob3c6IGZhbHNlLA0KICAgICAgcGxhdGVObzogJycsDQogICAgICAvLyDmrKDmrL7or6bmg4UNCiAgICAgIGRlYnREZXRhaWxWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1cnJlbnREZWJ0Um93OiBudWxsLA0KICAgICAgY2FyU3RhdHVzTGlzdDogWw0KICAgICAgICB7IGxhYmVsOiAn55yB5YaF5q2j5bi46KGM6am2JywgdmFsdWU6ICcxJyB9LA0KICAgICAgICB7IGxhYmVsOiAn55yB5aSW5q2j5bi46KGM6am2JywgdmFsdWU6ICcyJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5oq15oq8JywgdmFsdWU6ICczJyB9LA0KICAgICAgICB7IGxhYmVsOiAn55aR5Ly85oq15oq8JywgdmFsdWU6ICc0JyB9LA0KICAgICAgICB7IGxhYmVsOiAn55aR5Ly86buR6L2mJywgdmFsdWU6ICc1JyB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5YWl5bqTJywgdmFsdWU6ICc2JyB9LA0KICAgICAgICB7IGxhYmVsOiAn6L2m5Zyo5rOV6ZmiJywgdmFsdWU6ICc3JyB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5rOV5ouNJywgdmFsdWU6ICc4JyB9LA0KICAgICAgICB7IGxhYmVsOiAn5Y2P5ZWG5Y2W6L2mJywgdmFsdWU6ICc5JyB9LA0KICAgICAgXSwNCiAgICAgIGxpdGlnYXRpb25TdGF0dXNMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICflvoXnq4vmoYgnLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7Lnq4vmoYgnLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICflvIDluq0nLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICfliKTlhrMnLCB2YWx1ZTogJzQnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnu5PmoYgnLCB2YWx1ZTogJzUnIH0sDQogICAgICBdLA0KICAgICAgbG9nVHlwZUxpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+eUteivnScsIHZhbHVlOiAnMScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+efreS/oScsIHZhbHVlOiAnMicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+S4iumXqCcsIHZhbHVlOiAnMycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+mCruS7ticsIHZhbHVlOiAnNCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WFtuS7licsIHZhbHVlOiAnNScgfSwNCiAgICAgIF0sDQogICAgICBsYXdzdWl0Q291cnRMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICfms5XpmaJBJywgdmFsdWU6ICdBJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5rOV6ZmiQicsIHZhbHVlOiAnQicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+azlemZokMnLCB2YWx1ZTogJ0MnIH0sDQogICAgICBdLA0KICAgICAgbGF3c3VpdFR5cGVMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICflgLrovawnLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICflgLrliqAnLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICfmi4Xkv53nianmnYMnLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICfku7Loo4EnLCB2YWx1ZTogJzQnIH0sDQogICAgICAgIHsgbGFiZWw6ICfotYvlvLrlhazor4EnLCB2YWx1ZTogJzUnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmi43nirblhYMnLCB2YWx1ZTogJzYnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmi43lj7jku6QnLCB2YWx1ZTogJzcnIH0sDQogICAgICAgIHsgbGFiZWw6ICflsZ7lnLDor4norrwnLCB2YWx1ZTogJzgnIH0sDQogICAgICAgIHsgbGFiZWw6ICfkvZnlgLzotbfor4knLCB2YWx1ZTogJzknIH0sDQogICAgICAgIHsgbGFiZWw6ICflgLrmnYPlh7rllK4nLCB2YWx1ZTogJzEwJyB9LA0KICAgICAgICB7IGxhYmVsOiAn562+57qm5Zyw6K+J6K68JywgdmFsdWU6ICcxMScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eJueauiuivieiuvOmAmumBkycsIHZhbHVlOiAnMTInIH0sDQogICAgICBdLA0KICAgICAgbGF3c3VpdENvbnRlbnRMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICfpk7booYzku6Plgb/ph5Hpop0nLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICfku6PmiaPph5Hpop0nLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICfov53nuqbph5EnLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICflhbbku5bmrKDmrL4nLCB2YWx1ZTogJzQnIH0sDQogICAgICBdLA0KICAgICAgZGlzcGF0Y2hMb2FuSWQ6ICcnLA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouazleivieahiOS7tuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsaXN0TGl0aWdhdGlvbih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5saXRpZ2F0aW9uX2Nhc2VMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgLy8g6I635Y+W5rOV6K+J6LS555So5rGH5oC75pWw5o2uDQogICAgICAgIHRoaXMubG9hZExpdGlnYXRpb25Db3N0U3VtbWFyeSgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L295rOV6K+J6LS555So5rGH5oC75pWw5o2uICovDQogICAgbG9hZExpdGlnYXRpb25Db3N0U3VtbWFyeSgpIHsNCiAgICAgIC8vIOiOt+WPluaJgOacieahiOS7tklE77yM56Gu5L+d6L2s5o2i5Li65pWw5a2X57G75Z6LDQogICAgICBjb25zdCBjYXNlSWRzID0gdGhpcy5saXRpZ2F0aW9uX2Nhc2VMaXN0DQogICAgICAgIC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgY29uc3QgaWQgPSBpdGVtLuW6j+WPtw0KICAgICAgICAgIC8vIOehruS/nUlE5piv5pWw5a2X57G75Z6LDQogICAgICAgICAgcmV0dXJuIHR5cGVvZiBpZCA9PT0gJ3N0cmluZycgPyBwYXJzZUludChpZCkgOiBOdW1iZXIoaWQpDQogICAgICAgIH0pDQogICAgICAgIC5maWx0ZXIoaWQgPT4gaWQgJiYgIWlzTmFOKGlkKSkNCg0KICAgICAgaWYgKGNhc2VJZHMubGVuZ3RoID09PSAwKSByZXR1cm4NCg0KICAgICAgY29uc29sZS5sb2coJ+WPkemAgeeahOahiOS7tklE5YiX6KGoOicsIGNhc2VJZHMpDQoNCiAgICAgIC8vIOiwg+eUqEFQSeiOt+WPlui0ueeUqOaxh+aAuw0KICAgICAgZ2V0TGl0aWdhdGlvbkNvc3RTdW1tYXJ5KGNhc2VJZHMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5saXRpZ2F0aW9uQ29zdFN1bW1hcnkgPSByZXNwb25zZS5kYXRhIHx8IHt9DQogICAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWIsOeahOi0ueeUqOaxh+aAu+aVsOaNrjonLCB0aGlzLmxpdGlnYXRpb25Db3N0U3VtbWFyeSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bms5Xor4notLnnlKjmsYfmgLvlpLHotKU6JywgcmVzcG9uc2UubXNnKQ0KICAgICAgICAgIHRoaXMubGl0aWdhdGlvbkNvc3RTdW1tYXJ5ID0ge30NCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bms5Xor4notLnnlKjmsYfmgLvlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMubGl0aWdhdGlvbkNvc3RTdW1tYXJ5ID0ge30NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKiDojrflj5bms5Xor4notLnnlKjph5Hpop0gKi8NCiAgICBnZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgZmVlVHlwZSkgew0KICAgICAgLy8g56Gu5L+dY2FzZUlk5piv5q2j56Gu55qE57G75Z6LDQogICAgICBjb25zdCBub3JtYWxpemVkQ2FzZUlkID0gdHlwZW9mIGNhc2VJZCA9PT0gJ3N0cmluZycgPyBwYXJzZUludChjYXNlSWQpIDogTnVtYmVyKGNhc2VJZCkNCiAgICAgIGNvbnN0IHN1bW1hcnkgPSB0aGlzLmxpdGlnYXRpb25Db3N0U3VtbWFyeVtub3JtYWxpemVkQ2FzZUlkXQ0KDQogICAgICBpZiAoIXN1bW1hcnkpIHsNCiAgICAgICAgY29uc29sZS5sb2coYOacquaJvuWIsOahiOS7tklEICR7bm9ybWFsaXplZENhc2VJZH0g55qE6LS555So5rGH5oC75pWw5o2uYCkNCiAgICAgICAgcmV0dXJuIDANCiAgICAgIH0NCg0KICAgICAgc3dpdGNoIChmZWVUeXBlKSB7DQogICAgICAgIGNhc2UgJ2p1ZGdtZW50QW1vdW50JzoNCiAgICAgICAgICByZXR1cm4gTnVtYmVyKHN1bW1hcnkuanVkZ21lbnRBbW91bnQgfHwgMCkNCiAgICAgICAgY2FzZSAnaW50ZXJlc3QnOg0KICAgICAgICAgIHJldHVybiBOdW1iZXIoc3VtbWFyeS5pbnRlcmVzdCB8fCAwKQ0KICAgICAgICBjYXNlICdsaXRpZ2F0aW9uJzoNCiAgICAgICAgICAvLyDor4norrzotLnljIXlkKvlpJrnp43otLnnlKjnsbvlnovnmoTmgLvlkowNCiAgICAgICAgICByZXR1cm4gTnVtYmVyKHN1bW1hcnkubGF3eWVyRmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkubGl0aWdhdGlvbkZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LnByZXNlcnZhdGlvbkZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LnN1cnZlaWxsYW5jZUZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LmFubm91bmNlbWVudEZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LmFwcHJhaXNhbEZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LmV4ZWN1dGlvbkZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LnBlbmFsdHkgfHwgMCkgKw0KICAgICAgICAgICAgICAgICBOdW1iZXIoc3VtbWFyeS5ndWFyYW50ZWVGZWUgfHwgMCkgKw0KICAgICAgICAgICAgICAgICBOdW1iZXIoc3VtbWFyeS5pbnRlcm1lZGlhcnlGZWUgfHwgMCkgKw0KICAgICAgICAgICAgICAgICBOdW1iZXIoc3VtbWFyeS5jb21wZW5zaXR5IHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkub3RoZXJBbW91bnRzT3dlZCB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5Lmluc3VyYW5jZSB8fCAwKQ0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiAwDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmn6XnnIvor4norrzotLnor6bmg4UgKi8NCiAgICB2aWV3TGl0aWdhdGlvbkZlZURldGFpbHMoY2FzZUlkLCBmZWVUeXBlKSB7DQogICAgICAvLyDnoa7kv51jYXNlSWTmmK/mraPnoa7nmoTnsbvlnosNCiAgICAgIGNvbnN0IG5vcm1hbGl6ZWRDYXNlSWQgPSB0eXBlb2YgY2FzZUlkID09PSAnc3RyaW5nJyA/IHBhcnNlSW50KGNhc2VJZCkgOiBOdW1iZXIoY2FzZUlkKQ0KDQogICAgICAvLyDlj6rlpITnkIbor4norrzotLnor6bmg4UNCiAgICAgIGlmIChmZWVUeXBlID09PSAnbGl0aWdhdGlvbicpIHsNCiAgICAgICAgY29uc3QgdGl0bGUgPSAn6K+J6K686LS56K+m5oOFJw0KICAgICAgICBjb25zdCBjb250ZW50ID0gdGhpcy5mb3JtYXRMaXRpZ2F0aW9uRmVlRGV0YWlsKG5vcm1hbGl6ZWRDYXNlSWQsIFsNCiAgICAgICAgICAnbGF3eWVyRmVlJywgJ2xpdGlnYXRpb25GZWUnLCAncHJlc2VydmF0aW9uRmVlJywgJ3N1cnZlaWxsYW5jZUZlZScsDQogICAgICAgICAgJ2Fubm91bmNlbWVudEZlZScsICdhcHByYWlzYWxGZWUnLCAnZXhlY3V0aW9uRmVlJywgJ3BlbmFsdHknLA0KICAgICAgICAgICdndWFyYW50ZWVGZWUnLCAnaW50ZXJtZWRpYXJ5RmVlJywgJ2NvbXBlbnNpdHknLCAnb3RoZXJBbW91bnRzT3dlZCcsICdpbnN1cmFuY2UnDQogICAgICAgIF0pDQoNCiAgICAgICAgdGhpcy4kYWxlcnQoY29udGVudCwgdGl0bGUsIHsNCiAgICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmoLzlvI/ljJbms5Xor4notLnnlKjor6bmg4UgKi8NCiAgICBmb3JtYXRMaXRpZ2F0aW9uRmVlRGV0YWlsKGNhc2VJZCwgZmVlVHlwZXMpIHsNCiAgICAgIGNvbnN0IHN1bW1hcnkgPSB0aGlzLmxpdGlnYXRpb25Db3N0U3VtbWFyeVtjYXNlSWRdDQogICAgICBpZiAoIXN1bW1hcnkpIHJldHVybiAnPHA+5pqC5peg6LS555So5pWw5o2uPC9wPicNCg0KICAgICAgY29uc3QgZmVlTGFiZWxzID0gew0KICAgICAgICBqdWRnbWVudEFtb3VudDogJ+WIpOWGs+mHkeminScsDQogICAgICAgIGludGVyZXN0OiAn5Yip5oGvJywNCiAgICAgICAgbGF3eWVyRmVlOiAn5b6L5biI6LS5JywNCiAgICAgICAgbGl0aWdhdGlvbkZlZTogJ+azleiviei0uScsDQogICAgICAgIHByZXNlcnZhdGlvbkZlZTogJ+S/neWFqOi0uScsDQogICAgICAgIHN1cnZlaWxsYW5jZUZlZTogJ+W4g+aOp+i0uScsDQogICAgICAgIGFubm91bmNlbWVudEZlZTogJ+WFrOWRiui0uScsDQogICAgICAgIGFwcHJhaXNhbEZlZTogJ+ivhOS8sOi0uScsDQogICAgICAgIGV4ZWN1dGlvbkZlZTogJ+aJp+ihjOi0uScsDQogICAgICAgIHBlbmFsdHk6ICfov53nuqbph5EnLA0KICAgICAgICBndWFyYW50ZWVGZWU6ICfmi4Xkv53otLknLA0KICAgICAgICBpbnRlcm1lZGlhcnlGZWU6ICflsYXpl7TotLknLA0KICAgICAgICBjb21wZW5zaXR5OiAn5Luj5YG/6YeRJywNCiAgICAgICAgb3RoZXJBbW91bnRzT3dlZDogJ+WFtuS7luasoOasvicsDQogICAgICAgIGluc3VyYW5jZTogJ+S/nemZqei0uScNCiAgICAgIH0NCg0KICAgICAgbGV0IGh0bWwgPSAnPGRpdiBzdHlsZT0idGV4dC1hbGlnbjogbGVmdDsiPicNCiAgICAgIGxldCB0b3RhbCA9IDANCg0KICAgICAgZmVlVHlwZXMuZm9yRWFjaChmZWVUeXBlID0+IHsNCiAgICAgICAgY29uc3QgYW1vdW50ID0gTnVtYmVyKHN1bW1hcnlbZmVlVHlwZV0gfHwgMCkNCiAgICAgICAgaWYgKGFtb3VudCA+IDApIHsNCiAgICAgICAgICBodG1sICs9IGA8cD4ke2ZlZUxhYmVsc1tmZWVUeXBlXX06IO+/pSR7dGhpcy5mb3JtYXRNb25leShhbW91bnQpfTwvcD5gDQogICAgICAgICAgdG90YWwgKz0gYW1vdW50DQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIGlmIChmZWVUeXBlcy5sZW5ndGggPiAxICYmIHRvdGFsID4gMCkgew0KICAgICAgICBodG1sICs9IGA8aHI+PHA+PHN0cm9uZz7lkIjorqE6IO+/pSR7dGhpcy5mb3JtYXRNb25leSh0b3RhbCl9PC9zdHJvbmc+PC9wPmANCiAgICAgIH0NCg0KICAgICAgaHRtbCArPSAnPC9kaXY+Jw0KICAgICAgcmV0dXJuIGh0bWwgfHwgJzxwPuaaguaXoOi0ueeUqOaVsOaNrjwvcD4nDQogICAgfSwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHt9DQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvLyDlkK/liqjms5Xor4nlvLnnqpcNCiAgICBvcGVuTGl0aWdhdGlvbkZvcm0ocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cgfHwge30NCiAgICAgIHRoaXMuJHJlZnMubGl0aWdhdGlvbkZvcm0ub3BlbiAmJiB0aGlzLiRyZWZzLmxpdGlnYXRpb25Gb3JtLm9wZW4oKQ0KICAgIH0sDQogICAgb25MaXRpZ2F0aW9uRm9ybUNvbmZpcm0oKSB7DQogICAgICAvLyDlpITnkIbnoa7orqTpgLvovpENCiAgICB9LA0KICAgIC8vIOazleiviei0ueeUqOW8ueeqlw0KICAgIG9wZW5MaXRpZ2F0aW9uRmVlRm9ybShyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJvdyA9IHJvdyB8fCB7fQ0KICAgICAgdGhpcy4kcmVmcy5saXRpZ2F0aW9uRmVlRm9ybS5vcGVuICYmIHRoaXMuJHJlZnMubGl0aWdhdGlvbkZlZUZvcm0ub3BlbigpDQogICAgfSwNCiAgICBvbkxpdGlnYXRpb25GZWVGb3JtQ29uZmlybSgpIHsNCiAgICAgIC8vIOWkhOeQhuehruiupOmAu+i+kQ0KICAgIH0sDQogICAgLy8g5o+Q5Lqk5pel5b+X5by556qXDQogICAgb3BlbkxpdGlnYXRpb25Mb2dGb3JtKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKQ0KICAgICAgdGhpcy4kcmVmcy5saXRpZ2F0aW9uTG9nRm9ybS5vcGVuRGlhbG9nICYmIHRoaXMuJHJlZnMubGl0aWdhdGlvbkxvZ0Zvcm0ub3BlbkRpYWxvZygpDQogICAgfSwNCiAgICBvbkxpdGlnYXRpb25Mb2dGb3JtQ29uZmlybSgpIHsNCiAgICAgIC8vIOWkhOeQhuehruiupOmAu+i+kQ0KICAgIH0sDQogICAgLy8g5pel5b+X5p+l55yL5by556qXDQogICAgb3BlbkxpdGlnYXRpb25Mb2dWaWV3KHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93IHx8IHt9DQogICAgICB0aGlzLiRyZWZzLmxpdGlnYXRpb25Mb2dWaWV3Lm9wZW5EaWFsb2cgJiYgdGhpcy4kcmVmcy5saXRpZ2F0aW9uTG9nVmlldy5vcGVuRGlhbG9nKCkNCiAgICB9LA0KICAgIC8vIOaJvui9puaMiemSruW8ueeqlw0KICAgIG9wZW5EaXNwYXRjaFZlaGljbGVGb3JtKHJvdykgew0KICAgICAgdGhpcy5kaXNwYXRjaExvYW5JZCA9IHJvdy7mtYHnqIvluo/lj7cNCiAgICAgIHRoaXMuJHJlZnMuZGlzcGF0Y2hWZWhpY2xlRm9ybS5vcGVuRGlhbG9nKCkNCiAgICB9LA0KICAgIC8qKiDorqHnrpflvoXov73lgb/mrKDmrL7mgLvpop0gKi8NCiAgICBjYWxjdWxhdGVUb3RhbERlYnQocm93KSB7DQogICAgICBjb25zdCBjYXNlSWQgPSByb3cu5bqP5Y+3DQoNCiAgICAgIC8vIOiOt+WPluWQhOmhuei0ueeUqA0KICAgICAgY29uc3QganVkZ21lbnRBbW91bnQgPSB0aGlzLmdldExpdGlnYXRpb25GZWVBbW91bnQoY2FzZUlkLCAnanVkZ21lbnRBbW91bnQnKSAvLyDliKTlhrPph5Hpop0NCiAgICAgIGNvbnN0IGludGVyZXN0ID0gdGhpcy5nZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgJ2ludGVyZXN0JykgLy8g5Yip5oGvDQogICAgICBjb25zdCBsaXRpZ2F0aW9uQ29zdHMgPSB0aGlzLmdldExpdGlnYXRpb25GZWVBbW91bnQoY2FzZUlkLCAnbGl0aWdhdGlvbicpIC8vIOazleiviei0ueeUqA0KICAgICAgY29uc3QgdW5zdWVkQW1vdW50ID0gdGhpcy5jYWxjdWxhdGVVbnN1ZWRBbW91bnQocm93KSAvLyDmnKrotbfor4nph5Hpop0NCg0KICAgICAgLy8g6K6h566X5oC76aKd77ya5Yik5Yaz6YeR6aKdICsg5Yip5oGvICsg5rOV6K+J6LS555SoICsg5pyq6LW36K+J6YeR6aKdDQogICAgICBjb25zdCB0b3RhbCA9IE51bWJlcihqdWRnbWVudEFtb3VudCkgKyBOdW1iZXIoaW50ZXJlc3QpICsgTnVtYmVyKGxpdGlnYXRpb25Db3N0cykgKyBOdW1iZXIodW5zdWVkQW1vdW50KQ0KDQogICAgICByZXR1cm4gdG90YWwNCiAgICB9LA0KDQogICAgLyoqIOafpeeci+W+hei/veWBv+asoOasvuivpuaDhSAqLw0KICAgIHZpZXdUb3RhbERlYnREZXRhaWxzKHJvdykgew0KICAgICAgY29uc3QgY2FzZUlkID0gcm93LuW6j+WPtw0KDQogICAgICAvLyDojrflj5blkITpobnotLnnlKgNCiAgICAgIGNvbnN0IGp1ZGdtZW50QW1vdW50ID0gdGhpcy5nZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgJ2p1ZGdtZW50QW1vdW50JykNCiAgICAgIGNvbnN0IGludGVyZXN0ID0gdGhpcy5nZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgJ2ludGVyZXN0JykNCiAgICAgIGNvbnN0IGxpdGlnYXRpb25Db3N0cyA9IHRoaXMuZ2V0TGl0aWdhdGlvbkZlZUFtb3VudChjYXNlSWQsICdsaXRpZ2F0aW9uJykNCiAgICAgIGNvbnN0IHVuc3VlZEFtb3VudCA9IHRoaXMuY2FsY3VsYXRlVW5zdWVkQW1vdW50KHJvdykNCiAgICAgIGNvbnN0IHRvdGFsID0gdGhpcy5jYWxjdWxhdGVUb3RhbERlYnQocm93KQ0KDQogICAgICBsZXQgaHRtbCA9ICc8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBsZWZ0OyI+Jw0KICAgICAgaHRtbCArPSBgPHA+5Yik5Yaz6YeR6aKdOiDvv6Uke3RoaXMuZm9ybWF0TW9uZXkoanVkZ21lbnRBbW91bnQpfTwvcD5gDQogICAgICBodG1sICs9IGA8cD7liKnmga86IO+/pSR7dGhpcy5mb3JtYXRNb25leShpbnRlcmVzdCl9PC9wPmANCiAgICAgIGh0bWwgKz0gYDxwPuazleiviei0ueeUqDog77+lJHt0aGlzLmZvcm1hdE1vbmV5KGxpdGlnYXRpb25Db3N0cyl9PC9wPmANCiAgICAgIGh0bWwgKz0gYDxwPuacqui1t+iviemHkeminTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KHVuc3VlZEFtb3VudCl9PC9wPmANCiAgICAgIGh0bWwgKz0gYDxocj48cD48c3Ryb25nPuW+hei/veWBv+asoOasvuaAu+iuoTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KHRvdGFsKX08L3N0cm9uZz48L3A+YA0KICAgICAgaHRtbCArPSAnPC9kaXY+Jw0KDQogICAgICB0aGlzLiRhbGVydChodG1sLCAn5b6F6L+95YG/5qyg5qy+6K+m5oOFJywgew0KICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJw0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5omT5byA5pel5bi46LS555So55Sz6K+35by556qXDQogICAgb3BlbkRhaWx5RXhwZW5zZURpYWxvZyhyb3cpIHsNCiAgICAgIC8vIOmAmui/h3JlZuiwg+eUqOazleiviei0ueeUqOihqOWNlee7hOS7tueahOaWueazle+8jOS8oOWFpeahiOS7tklEDQogICAgICB0aGlzLiRyZWZzLmxpdGlnYXRpb25GZWVGb3JtLm9wZW5EYWlseUV4cGVuc2VEaWFsb2cocm93LuW6j+WPtykNCiAgICB9LA0KICAgIC8vIOafpeeci+i0t+asvuS6uuS/oeaBrw0KICAgIG9wZW5Vc2VySW5mbyhjdXN0b21lckluZm8pIHsNCiAgICAgIHRoaXMuY3VzdG9tZXJJbmZvID0gY3VzdG9tZXJJbmZvDQogICAgICB0aGlzLnVzZXJJbmZvVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOafpeeci+i9pui+huS/oeaBrw0KICAgIGNoZWNrQ2FyKHBsYXRlTm8pIHsNCiAgICAgIHRoaXMucGxhdGVObyA9IHBsYXRlTm8NCiAgICAgIHRoaXMuY2FyU2hvdyA9IHRydWUNCiAgICB9LA0KICAgIC8vIOiOt+WPlui1t+ivieexu+Wei+aWh+Wtlw0KICAgIGdldExhd3N1aXRUeXBlVGV4dCh2YWx1ZSkgew0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMubGF3c3VpdFR5cGVMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSB2YWx1ZSkNCiAgICAgIHJldHVybiBpdGVtID8gaXRlbS5sYWJlbCA6IHZhbHVlDQogICAgfSwNCiAgICAvLyDojrflj5botbfor4nlhoXlrrnmloflrZcNCiAgICBnZXRMYXdzdWl0Q29udGVudFRleHQodmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUpIHJldHVybiAnLScNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5bCd6K+V6Kej5p6QSlNPTuaVsOe7hO+8iOWkmumAieagvOW8j++8iQ0KICAgICAgICBjb25zdCBjb250ZW50QXJyYXkgPSBKU09OLnBhcnNlKHZhbHVlKQ0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShjb250ZW50QXJyYXkpKSB7DQogICAgICAgICAgcmV0dXJuIGNvbnRlbnRBcnJheS5tYXAodmFsID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmxhd3N1aXRDb250ZW50TGlzdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gdmFsKQ0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0gPyBpdGVtLmxhYmVsIDogdmFsDQogICAgICAgICAgfSkuam9pbign44CBJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCB7DQogICAgICAgIC8vIOWmguaenOS4jeaYr0pTT07moLzlvI/vvIzmjInljZXpgInlpITnkIYNCiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMubGF3c3VpdENvbnRlbnRMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSB2YWx1ZSkNCiAgICAgICAgcmV0dXJuIGl0ZW0gPyBpdGVtLmxhYmVsIDogdmFsdWUNCiAgICAgIH0NCg0KICAgICAgLy8g6buY6K6k6L+U5Zue5Y6f5YC8DQogICAgICByZXR1cm4gdmFsdWUNCiAgICB9LA0KICAgIC8vIOiOt+WPluWCrOiusOexu+Wei+aWh+acrA0KICAgIGdldFVyZ2VTdGF0dXNUZXh0KHZhbHVlKSB7DQogICAgICBjb25zdCB1cmdlU3RhdHVzTWFwID0gew0KICAgICAgICAxOiAn57un57ut6Lef6LiqJywNCiAgICAgICAgMjogJ+e6puWumui/mOasvicsDQogICAgICAgIDM6ICfml6Dms5Xot5/ov5snDQogICAgICB9DQogICAgICByZXR1cm4gdXJnZVN0YXR1c01hcFt2YWx1ZV0gfHwgdmFsdWUNCiAgICB9LA0KICAgIC8vIOiOt+WPlui9pui+hueKtuaAgeaWh+acrA0KICAgIGdldENhclN0YXR1c1RleHQodmFsdWUpIHsNCiAgICAgIC8vIOS9v+eUqOW3suacieeahCBjYXJTdGF0dXNMaXN0IOaVsOaNrg0KICAgICAgY29uc3Qgc3RhdHVzSXRlbSA9IHRoaXMuY2FyU3RhdHVzTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PSB2YWx1ZSkNCiAgICAgIHJldHVybiBzdGF0dXNJdGVtID8gc3RhdHVzSXRlbS5sYWJlbCA6IHZhbHVlDQogICAgfSwNCiAgICAvLyDmmL7npLrmrKDmrL7or6bmg4XlvLnnqpcNCiAgICBzaG93RGVidERldGFpbChyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudERlYnRSb3cgPSByb3cNCiAgICAgIHRoaXMuZGVidERldGFpbFZpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICAvLyDmoLzlvI/ljJbph5Hpop3mmL7npLoNCiAgICBmb3JtYXRNb25leShhbW91bnQpIHsNCiAgICAgIGlmIChhbW91bnQgPT09IG51bGwgfHwgYW1vdW50ID09PSB1bmRlZmluZWQgfHwgYW1vdW50ID09PSAnJykgew0KICAgICAgICByZXR1cm4gJzAuMDAnDQogICAgICB9DQogICAgICByZXR1cm4gTnVtYmVyKGFtb3VudCkudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsDQogICAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOiuoeeul+acqui1t+iviemHkeminQ0KICAgIGNhbGN1bGF0ZVVuc3VlZEFtb3VudChyb3cpIHsNCiAgICAgIGNvbnN0IHJlbWFpbmluZ0Ftb3VudCA9IE51bWJlcihyb3cu5Ymp5L2Z6YeR6aKdKSB8fCAwDQogICAgICBjb25zdCBzdWVkQW1vdW50ID0gTnVtYmVyKHJvdy7otbfor4nph5Hpop0pIHx8IDANCiAgICAgIGNvbnN0IHVuc3VlZEFtb3VudCA9IHJlbWFpbmluZ0Ftb3VudCAtIHN1ZWRBbW91bnQNCiAgICAgIHJldHVybiB1bnN1ZWRBbW91bnQgPiAwID8gdW5zdWVkQW1vdW50IDogMA0KICAgIH0sDQogIH0sDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "index.vue", "sourceRoot": "src/views/litigation/litigation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 启动法诉弹窗 -->\r\n    <litigation-form ref=\"litigationForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 法诉费用弹窗 -->\r\n    <litigation-fee-form ref=\"litigationFeeForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 提交日志弹窗 -->\r\n    <litigation-log-form ref=\"litigationLogForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 日志查看弹窗 -->\r\n    <litigation-log-view ref=\"litigationLogView\" :data=\"currentRow\" />\r\n\r\n    <!-- 派单找车组件 -->\r\n    <dispatch-vehicle-form ref=\"dispatchVehicleForm\" :loanId=\"dispatchLoanId\" />\r\n\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input v-model=\"queryParams.certId\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"车牌号码\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"carStatus\">\r\n        <el-select v-model=\"queryParams.carStatus\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"litigationClerk\">\r\n        <el-input v-model=\"queryParams.litigationClerk\" placeholder=\"法诉文员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <template v-if=\"showMore\">\r\n        <el-form-item label=\"\" prop=\"caseOwner\">\r\n          <el-input v-model=\"queryParams.caseOwner\" placeholder=\"案件负责人\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"litigationStatus\">\r\n          <litigation-status v-model=\"queryParams.litigationStatus\" placeholder=\"法诉状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"logType\">\r\n          <el-select v-model=\"queryParams.logType\" placeholder=\"日志类型\" clearable>\r\n            <el-option v-for=\"dict in logTypeList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"lawsuitCourt\">\r\n          <el-select v-model=\"queryParams.lawsuitCourt\" placeholder=\"诉讼法院\" clearable>\r\n            <el-option v-for=\"dict in lawsuitCourtList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n      <el-form-item style=\"float: right\">\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"text\" size=\"mini\" @click=\"showMore = !showMore\">\r\n          {{ showMore ? '收起' : '更多' }}\r\n          <i :class=\"showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"litigation_caseList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"法诉文员\" align=\"center\" prop=\"法诉文员\" width=\"100\" />\r\n      <el-table-column label=\"发起法诉日\" align=\"center\" prop=\"发起法诉日\" width=\"110\" />\r\n      <el-table-column label=\"案件启动日\" align=\"center\" prop=\"案件启动日\" width=\"110\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getUrgeStatusText(scope.row.日志类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"日志更新日\" align=\"center\" prop=\"日志更新日\" width=\"110\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"法诉子状态\" width=\"100\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"贷款人\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openUserInfo({ customerId: scope.row.客户ID, applyId: scope.row.申请编号 })\">\r\n            {{ scope.row.贷款人 }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"身份证\" align=\"center\" prop=\"身份证\" width=\"150\" />\r\n      <el-table-column label=\"车辆牌号\" align=\"center\" prop=\"车辆牌号\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"checkCar(scope.row.车辆牌号)\">{{ scope.row.车辆牌号 }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆状态\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getCarStatusText(scope.row.车辆状态) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"找车团队\" align=\"center\" width=\"100\">\r\n        <template #default=\"{ row }\">\r\n          {{ row.找车团队 || '未派单' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"地区\" width=\"100\" />\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"出单渠道\" width=\"120\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"放款银行\" width=\"120\" />\r\n      <el-table-column label=\"托管类型\" align=\"center\" prop=\"托管类型\" width=\"100\" />\r\n      <el-table-column label=\"欠款余额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(scope.row.剩余金额) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showDebtDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateUnsuedAmount(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showUnsuedContentDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.起诉金额) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉类型\" align=\"center\" prop=\"起诉类型\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitTypeText(scope.row.起诉类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉内容\" align=\"center\" prop=\"起诉内容\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitContentText(scope.row.起诉内容) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"判决金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'judgmentAmount')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"利息\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'interest')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"诉讼费\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'litigation')) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewLitigationFeeDetails(scope.row.序号, 'litigation')\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"待追偿欠款\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateTotalDebt(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewTotalDebtDetails(scope.row)\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"代偿证明发出日\" align=\"center\" prop=\"代偿证明发出日\" width=\"140\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"法院地\" width=\"100\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"诉讼法院\" width=\"120\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"案件负责人\" width=\"100\" />\r\n      <el-table-column label=\"诉前调号出具时间\" align=\"center\" prop=\"诉前调号出具时间\" width=\"150\" />\r\n      <el-table-column label=\"诉前调号\" align=\"center\" prop=\"诉前调号\" width=\"120\" />\r\n      <el-table-column label=\"民初号出具时间\" align=\"center\" prop=\"民初号出具时间\" width=\"140\" />\r\n      <el-table-column label=\"民初号\" align=\"center\" prop=\"民初号\" width=\"120\" />\r\n      <el-table-column label=\"开庭时间\" align=\"center\" prop=\"开庭时间\" width=\"110\" />\r\n      <el-table-column label=\"申请执行时间\" align=\"center\" prop=\"申请执行时间\" width=\"130\" />\r\n      <el-table-column label=\"执行号/执保号\" align=\"center\" prop=\"执行号/执保号\" width=\"140\" />\r\n      <el-table-column label=\"车辆出库时间\" align=\"center\" prop=\"车辆出库时间\" width=\"130\" />\r\n      <el-table-column label=\"法拍时间\" align=\"center\" prop=\"法拍时间\" width=\"110\" />\r\n      <el-table-column label=\"车辆评估价\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.车辆评估价) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"拍卖金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.拍卖金额) }}\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogView(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                查看日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n                @click=\"openLitigationForm(scope.row)\">\r\n                启动法诉\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                提交日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationFeeForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                法诉费用\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDispatchVehicleForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                派单找车\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDailyExpenseDialog(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                日常费用\r\n              </el-button>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <car-info ref=\"carInfo\" :visible.sync=\"carShow\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n\r\n    <!-- 欠款详情弹窗 -->\r\n    <el-dialog title=\"欠款详情\" :visible.sync=\"debtDetailVisible\" width=\"800px\" append-to-body>\r\n      <div v-if=\"currentDebtRow\" style=\"padding: 10px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">银行代偿</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行代偿金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行代偿金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.银行剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">代扣金额</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.代扣剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">违约金</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.违约金) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回违约金金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还违约金:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还违约金金额) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">其他欠款</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还其他欠款:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还其他欠款) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <div style=\"margin-top: 20px; padding-top: 15px; border-top: 2px solid #409EFF; background-color: #f5f7fa; padding: 15px; border-radius: 4px;\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">汇总信息</h4>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">总欠款金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.总欠款金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">已还金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.已还金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">剩余金额:</span>\r\n                <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation, getLitigationCostSummary, getLitigationByLoanId } from '@/api/litigation/litigation'\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport litigationForm from './modules/litigationForm'\r\nimport litigationFeeForm from './modules/litigationFeeForm'\r\nimport litigationLogForm from './modules/litigationLogForm'\r\nimport litigationLogView from './modules/litigationLogView'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport dispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'\r\n\r\nexport default {\r\n  name: 'Litigation',\r\n  components: {\r\n    litigationStatus,\r\n    litigationForm,\r\n    litigationFeeForm,\r\n    litigationLogForm,\r\n    litigationLogView,\r\n    userInfo,\r\n    carInfo,\r\n    dispatchVehicleForm,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 法诉案件表格数据\r\n      litigation_caseList: [],\r\n      // 法诉费用汇总数据 - 按案件ID存储\r\n      litigationCostSummary: {},\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: '',\r\n        certId: '',\r\n        plateNo: '',\r\n        carStatus: '',\r\n        jgName: '',\r\n        caseOwner: '',\r\n        litigationStatus: '',\r\n        logType: '',\r\n        lawsuitCourt: '',\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前行数据\r\n      currentRow: {},\r\n      showMore: false,\r\n      // 贷款人信息\r\n      userInfoVisible: false,\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      // 车辆信息\r\n      carShow: false,\r\n      plateNo: '',\r\n      // 欠款详情\r\n      debtDetailVisible: false,\r\n      currentDebtRow: null,\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      litigationStatusList: [\r\n        { label: '待立案', value: '1' },\r\n        { label: '已立案', value: '2' },\r\n        { label: '开庭', value: '3' },\r\n        { label: '判决', value: '4' },\r\n        { label: '结案', value: '5' },\r\n      ],\r\n      logTypeList: [\r\n        { label: '电话', value: '1' },\r\n        { label: '短信', value: '2' },\r\n        { label: '上门', value: '3' },\r\n        { label: '邮件', value: '4' },\r\n        { label: '其他', value: '5' },\r\n      ],\r\n      lawsuitCourtList: [\r\n        { label: '法院A', value: 'A' },\r\n        { label: '法院B', value: 'B' },\r\n        { label: '法院C', value: 'C' },\r\n      ],\r\n      lawsuitTypeList: [\r\n        { label: '债转', value: '1' },\r\n        { label: '债加', value: '2' },\r\n        { label: '担保物权', value: '3' },\r\n        { label: '仲裁', value: '4' },\r\n        { label: '赋强公证', value: '5' },\r\n        { label: '拍状元', value: '6' },\r\n        { label: '拍司令', value: '7' },\r\n        { label: '属地诉讼', value: '8' },\r\n        { label: '余值起诉', value: '9' },\r\n        { label: '债权出售', value: '10' },\r\n        { label: '签约地诉讼', value: '11' },\r\n        { label: '特殊诉讼通道', value: '12' },\r\n      ],\r\n      lawsuitContentList: [\r\n        { label: '银行代偿金额', value: '1' },\r\n        { label: '代扣金额', value: '2' },\r\n        { label: '违约金', value: '3' },\r\n        { label: '其他欠款', value: '4' },\r\n      ],\r\n      dispatchLoanId: '',\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉案件列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigation(this.queryParams).then(response => {\r\n        this.litigation_caseList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n        // 获取法诉费用汇总数据\r\n        this.loadLitigationCostSummary()\r\n      })\r\n    },\r\n\r\n    /** 加载法诉费用汇总数据 */\r\n    loadLitigationCostSummary() {\r\n      // 获取所有案件ID，确保转换为数字类型\r\n      const caseIds = this.litigation_caseList\r\n        .map(item => {\r\n          const id = item.序号\r\n          // 确保ID是数字类型\r\n          return typeof id === 'string' ? parseInt(id) : Number(id)\r\n        })\r\n        .filter(id => id && !isNaN(id))\r\n\r\n      if (caseIds.length === 0) return\r\n\r\n      console.log('发送的案件ID列表:', caseIds)\r\n\r\n      // 调用API获取费用汇总\r\n      getLitigationCostSummary(caseIds).then(response => {\r\n        if (response.code === 200) {\r\n          this.litigationCostSummary = response.data || {}\r\n          console.log('获取到的费用汇总数据:', this.litigationCostSummary)\r\n        } else {\r\n          console.error('获取法诉费用汇总失败:', response.msg)\r\n          this.litigationCostSummary = {}\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取法诉费用汇总失败:', error)\r\n        this.litigationCostSummary = {}\r\n      })\r\n    },\r\n\r\n    /** 获取法诉费用金额 */\r\n    getLitigationFeeAmount(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n      const summary = this.litigationCostSummary[normalizedCaseId]\r\n\r\n      if (!summary) {\r\n        console.log(`未找到案件ID ${normalizedCaseId} 的费用汇总数据`)\r\n        return 0\r\n      }\r\n\r\n      switch (feeType) {\r\n        case 'judgmentAmount':\r\n          return Number(summary.judgmentAmount || 0)\r\n        case 'interest':\r\n          return Number(summary.interest || 0)\r\n        case 'litigation':\r\n          // 诉讼费包含多种费用类型的总和\r\n          return Number(summary.lawyerFee || 0) +\r\n                 Number(summary.litigationFee || 0) +\r\n                 Number(summary.preservationFee || 0) +\r\n                 Number(summary.surveillanceFee || 0) +\r\n                 Number(summary.announcementFee || 0) +\r\n                 Number(summary.appraisalFee || 0) +\r\n                 Number(summary.executionFee || 0) +\r\n                 Number(summary.penalty || 0) +\r\n                 Number(summary.guaranteeFee || 0) +\r\n                 Number(summary.intermediaryFee || 0) +\r\n                 Number(summary.compensity || 0) +\r\n                 Number(summary.otherAmountsOwed || 0) +\r\n                 Number(summary.insurance || 0)\r\n        default:\r\n          return 0\r\n      }\r\n    },\r\n\r\n    /** 查看诉讼费详情 */\r\n    viewLitigationFeeDetails(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n\r\n      // 只处理诉讼费详情\r\n      if (feeType === 'litigation') {\r\n        const title = '诉讼费详情'\r\n        const content = this.formatLitigationFeeDetail(normalizedCaseId, [\r\n          'lawyerFee', 'litigationFee', 'preservationFee', 'surveillanceFee',\r\n          'announcementFee', 'appraisalFee', 'executionFee', 'penalty',\r\n          'guaranteeFee', 'intermediaryFee', 'compensity', 'otherAmountsOwed', 'insurance'\r\n        ])\r\n\r\n        this.$alert(content, title, {\r\n          dangerouslyUseHTMLString: true,\r\n          confirmButtonText: '确定'\r\n        })\r\n      }\r\n    },\r\n\r\n    /** 格式化法诉费用详情 */\r\n    formatLitigationFeeDetail(caseId, feeTypes) {\r\n      const summary = this.litigationCostSummary[caseId]\r\n      if (!summary) return '<p>暂无费用数据</p>'\r\n\r\n      const feeLabels = {\r\n        judgmentAmount: '判决金额',\r\n        interest: '利息',\r\n        lawyerFee: '律师费',\r\n        litigationFee: '法诉费',\r\n        preservationFee: '保全费',\r\n        surveillanceFee: '布控费',\r\n        announcementFee: '公告费',\r\n        appraisalFee: '评估费',\r\n        executionFee: '执行费',\r\n        penalty: '违约金',\r\n        guaranteeFee: '担保费',\r\n        intermediaryFee: '居间费',\r\n        compensity: '代偿金',\r\n        otherAmountsOwed: '其他欠款',\r\n        insurance: '保险费'\r\n      }\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      let total = 0\r\n\r\n      feeTypes.forEach(feeType => {\r\n        const amount = Number(summary[feeType] || 0)\r\n        if (amount > 0) {\r\n          html += `<p>${feeLabels[feeType]}: ￥${this.formatMoney(amount)}</p>`\r\n          total += amount\r\n        }\r\n      })\r\n\r\n      if (feeTypes.length > 1 && total > 0) {\r\n        html += `<hr><p><strong>合计: ￥${this.formatMoney(total)}</strong></p>`\r\n      }\r\n\r\n      html += '</div>'\r\n      return html || '<p>暂无费用数据</p>'\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {}\r\n      this.getList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 启动法诉弹窗\r\n    openLitigationForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationForm.open && this.$refs.litigationForm.open()\r\n    },\r\n    onLitigationFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 法诉费用弹窗\r\n    openLitigationFeeForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationFeeForm.open && this.$refs.litigationFeeForm.open()\r\n    },\r\n    onLitigationFeeFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 提交日志弹窗\r\n    openLitigationLogForm(row) {\r\n      this.currentRow = JSON.parse(JSON.stringify(row))\r\n      this.$refs.litigationLogForm.openDialog && this.$refs.litigationLogForm.openDialog()\r\n    },\r\n    onLitigationLogFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 日志查看弹窗\r\n    openLitigationLogView(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationLogView.openDialog && this.$refs.litigationLogView.openDialog()\r\n    },\r\n    // 找车按钮弹窗\r\n    openDispatchVehicleForm(row) {\r\n      this.dispatchLoanId = row.流程序号\r\n      this.$refs.dispatchVehicleForm.openDialog()\r\n    },\r\n    /** 计算待追偿欠款总额 */\r\n    calculateTotalDebt(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount') // 判决金额\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest') // 利息\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation') // 法诉费用\r\n      const unsuedAmount = this.calculateUnsuedAmount(row) // 未起诉金额\r\n\r\n      // 计算总额：判决金额 + 利息 + 法诉费用 + 未起诉金额\r\n      const total = Number(judgmentAmount) + Number(interest) + Number(litigationCosts) + Number(unsuedAmount)\r\n\r\n      return total\r\n    },\r\n\r\n    /** 查看待追偿欠款详情 */\r\n    viewTotalDebtDetails(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount')\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest')\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation')\r\n      const unsuedAmount = this.calculateUnsuedAmount(row)\r\n      const total = this.calculateTotalDebt(row)\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      html += `<p>判决金额: ￥${this.formatMoney(judgmentAmount)}</p>`\r\n      html += `<p>利息: ￥${this.formatMoney(interest)}</p>`\r\n      html += `<p>法诉费用: ￥${this.formatMoney(litigationCosts)}</p>`\r\n      html += `<p>未起诉金额: ￥${this.formatMoney(unsuedAmount)}</p>`\r\n      html += `<hr><p><strong>待追偿欠款总计: ￥${this.formatMoney(total)}</strong></p>`\r\n      html += '</div>'\r\n\r\n      this.$alert(html, '待追偿欠款详情', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '确定'\r\n      })\r\n    },\r\n\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(row) {\r\n      // 通过ref调用法诉费用表单组件的方法，传入案件ID\r\n      this.$refs.litigationFeeForm.openDailyExpenseDialog(row.序号)\r\n    },\r\n    // 查看贷款人信息\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    // 查看车辆信息\r\n    checkCar(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carShow = true\r\n    },\r\n    // 获取起诉类型文字\r\n    getLawsuitTypeText(value) {\r\n      const item = this.lawsuitTypeList.find(item => item.value === value)\r\n      return item ? item.label : value\r\n    },\r\n    // 获取起诉内容文字\r\n    getLawsuitContentText(value) {\r\n      if (!value) return '-'\r\n\r\n      try {\r\n        // 尝试解析JSON数组（多选格式）\r\n        const contentArray = JSON.parse(value)\r\n        if (Array.isArray(contentArray)) {\r\n          return contentArray.map(val => {\r\n            const item = this.lawsuitContentList.find(item => item.value === val)\r\n            return item ? item.label : val\r\n          }).join('、')\r\n        }\r\n      } catch {\r\n        // 如果不是JSON格式，按单选处理\r\n        const item = this.lawsuitContentList.find(item => item.value === value)\r\n        return item ? item.label : value\r\n      }\r\n\r\n      // 默认返回原值\r\n      return value\r\n    },\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value\r\n    },\r\n    // 获取车辆状态文本\r\n    getCarStatusText(value) {\r\n      // 使用已有的 carStatusList 数据\r\n      const statusItem = this.carStatusList.find(item => item.value == value)\r\n      return statusItem ? statusItem.label : value\r\n    },\r\n    // 显示欠款详情弹窗\r\n    showDebtDetail(row) {\r\n      this.currentDebtRow = row\r\n      this.debtDetailVisible = true\r\n    },\r\n    // 格式化金额显示\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n    // 计算未起诉金额\r\n    calculateUnsuedAmount(row) {\r\n      const remainingAmount = Number(row.剩余金额) || 0\r\n      const suedAmount = Number(row.起诉金额) || 0\r\n      const unsuedAmount = remainingAmount - suedAmount\r\n      return unsuedAmount > 0 ? unsuedAmount : 0\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 6px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n\r\n/* 欠款详情样式 */\r\n.debt-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.debt-label {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.debt-value {\r\n  font-size: 13px;\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.debt-remaining {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.total-debt {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.total-debt .debt-label {\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.total-debt .debt-value {\r\n  font-size: 15px;\r\n  color: #409EFF;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"]}]}